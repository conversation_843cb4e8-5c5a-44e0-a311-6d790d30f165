import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { OrganizationData } from "@/features/organizations/types";
import { useAuthStore } from "../stores/auth-store";
import { User } from "../types/user";

type Credential = {
  email: string;
  password: string;
  organization_unique_id: string;
};

export type Auth = {
  auth_token: string;
  authenticated: boolean;
  user: User & { organization: OrganizationData };
};

export const login = (data: Credential): ApiResponse<Auth> => {
  return api.post(API_ROUTES.LOGIN, data);
};

type UseLogin = {
  mutationConfig?: MutationConfig<typeof login>;
};

export const useLogin = ({ mutationConfig = {} }: UseLogin) => {
  const queryClient = useQueryClient();
  const { setRefreshToken } = useAuthStore();

  const { onSuccess, notification, ...restConfig } = mutationConfig;

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.setQueryData([QUERY_KEYS.AUTH_INFO], () => args[0]);
      setRefreshToken(args[0].data.auth_token);

      if (notification) {
        toast("You have successfully logged in.");
      }

      await onSuccess?.(...args);
    },

    ...restConfig,
    throwOnError: (_) => {
      return false;
    },
    mutationFn: login,
  });
};
