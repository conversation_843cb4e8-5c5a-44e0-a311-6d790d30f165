import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APSvot } from "@/features/account-plan/types/position-types";

type SvotListParams = BaseParams;

export const getSvotList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: SvotListParams;
}): ApiResponse<APSvot[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_SVOT(accountId), { params });
};

export const getSvotListQueryOptions = (
  accountId: number,
  params?: SvotListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_SVOT,
    ],
    queryFn: () => getSvotList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseSvotListOptions = {
  params?: SvotListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getSvotList>;
  options?: Partial<ReturnType<typeof getSvotListQueryOptions>>;
};

export const useSvotList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseSvotListOptions) => {
  const svotListQuery = useQuery({
    ...getSvotListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...svotListQuery,
    svotList: svotListQuery.data?.data,
  };
};
