import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { ForgotPasswordPayload } from "../../types";

export const forgotPassword = ({
  data,
}: {
  data?: ForgotPasswordPayload;
}): ApiResponse => {
  return api.post(`${API_ROUTES.USER_MANAGEMENTS_FORGOT_PASSWORD}`, data);
};

type UseForgotPasswordOptions = {
  mutationConfig?: MutationConfig<typeof forgotPassword>;
};

export const useForgotPassword = ({
  mutationConfig,
}: UseForgotPasswordOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: forgotPassword,
  });
};
