import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { ChangePasswordPayload } from "../../types";

export const changePassword = ({
  data,
}: {
  data?: ChangePasswordPayload;
}): ApiResponse => {
  return api.post(`${API_ROUTES.USER_MANAGEMENTS_CHANGE_PASSWORD}`, data);
};

type UseChangePasswordOptions = {
  mutationConfig?: MutationConfig<typeof changePassword>;
};

export const useChangePassword = ({
  mutationConfig,
}: UseChangePasswordOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: changePassword,
  });
};
