import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APHistoricRevenue } from "@/features/account-plan/types/revenue-types";

export const getHistoricRevenueDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APHistoricRevenue> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_HISTORIC_REVENUE_DETAIL(accountId, id)
  );
};

export const getHistoricRevenueDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_HISTORIC_REVENUE,
      id,
    ],
    queryFn: () => getHistoricRevenueDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseHistoricRevenueDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getHistoricRevenueDetail>;
  options?: Partial<ReturnType<typeof getHistoricRevenueDetailQueryOptions>>;
};

export const useHistoricRevenueDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseHistoricRevenueDetailOptions) => {
  const historicRevenueDetailQuery = useQuery({
    ...getHistoricRevenueDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...historicRevenueDetailQuery,
    historicRevenueDetail: historicRevenueDetailQuery.data?.data,
  };
};
