import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deleteMissingInformation = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(
    API_ROUTES.ACCOUNT_PLANS_MISSING_INFORMATION_DETAIL(accountId, id)
  );
};

type UseDeleteMissingInformationOptions = {
  mutationConfig?: MutationConfig<typeof deleteMissingInformation>;
};

export const useDeleteMissingInformation = ({
  mutationConfig,
}: UseDeleteMissingInformationOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_MISSING_INFORMATION,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteMissingInformation,
  });
};
