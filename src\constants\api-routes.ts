export const API_ROUTES = {
  AUTH: "/auth",
  LOGIN: "/login",
  S3_UPLOAD: "/s3/uploads/presign",
  USERS: "/users",
  USERS_DETAIL: (userId: number) => `/users/${userId}`,
  ORGANIZATIONS: "/organizations",
  ORGANIZATIONS_DETAIL: (organizationId?: number) =>
    `/organizations/${organizationId}`,

  UTILITIES_CURRENCY_EXCHANGE_RATES: "/utilities/currency_exchange_rates",

  MODEL_TEMPLATES: "/model_templates",
  MODEL_TEMPLATES_VARIABLES: "/model_template_variables",
  MODEL_TEMPLATES_CATEGORIES: "/template_categories",

  USER_MANAGEMENTS: "/user_managements",
  USER_MANAGEMENTS_FORGOT_PASSWORD: "/user_managements/change_password_request",
  USER_MANAGEMENTS_CHANGE_PASSWORD: "/user_managements/change_password",
  USER_MANAGEMENTS_DETAIL: (userId: number) => `/user_managements/${userId}`,
  USER_MANAGEMENTS_INVITE: "/user_managements/invite",
  USER_MANAGEMENTS_ACCEPT_INVITATION: "/user_managements/accept_invitation",
  USER_MANAGEMENTS_INVITATION_CODE_DETAILS:
    "/user_managements/invitation_code_details",
  USER_MANAGEMENTS_USER_PERMISSIONS: "/user_managements/user_permissions",
  USER_MANAGEMENTS_USER_PERMISSIONS_DETAIL: (userId: number) =>
    `/user_managements/user_permissions/${userId}`,

  ACCOUNT_PLAN_GROUPS: "/account_plan_groups",
  ACCOUNT_PLAN_GROUPS_DETAIL: (id?: number) => `/account_plan_groups/${id}`,
  ACCOUNT_PLANS: "/account_plans",
  ACCOUNT_PLANS_DETAIL: (id: number) => `/account_plans/${id}`,

  /* Account Plan Position API Routes */
  CRM_CONTACTS: (accountId: number) =>
    `/account_plans/${accountId}/crm_contacts`,
  ACCOUNT_PLANS_STAKEHOLDER_MAPPING: (accountId: number) =>
    `/account_plans/${accountId}/stakeholder_mapping`,
  ACCOUNT_PLANS_STAKEHOLDER_MAPPING_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/stakeholder_mapping/${id}`,

  ACCOUNT_PLANS_WALLET_SHARE: (accountId: number) =>
    `/account_plans/${accountId}/wallet_share`,
  ACCOUNT_PLANS_WALLET_SHARE_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/wallet_share/${id}`,

  ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS: (accountId: number) =>
    `/account_plans/${accountId}/circumstantial_analysis`,
  ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS_GENERATE: (accountId: number) =>
    `/account_plans/${accountId}/circumstantial_analysis/generate`,
  ACCOUNT_PLANS_CIRCUMSTANTIAL_ANALYSIS_DETAIL: (
    accountId: number,
    id: number
  ) => `/account_plans/${accountId}/circumstantial_analysis/${id}`,

  ACCOUNT_PLANS_SVOT: (accountId: number) => `/account_plans/${accountId}/svot`,
  ACCOUNT_PLANS_SVOT_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/svot/${id}`,

  ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE: (accountId: number) =>
    `/account_plans/${accountId}/insight_and_perspective`,
  ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE_GENERATE: (accountId: number) =>
    `/account_plans/${accountId}/insight_and_perspective/generate`,
  ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE_DETAIL: (
    accountId: number,
    id: number
  ) => `/account_plans/${accountId}/insight_and_perspective/${id}`,

  /* Account Plan Revenue API Routes */
  ACCOUNT_PLANS_HISTORIC_REVENUE: (accountId: number) =>
    `/account_plans/${accountId}/historic_revenue`,
  ACCOUNT_PLANS_HISTORIC_REVENUE_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/historic_revenue/${id}`,

  ACCOUNT_PLANS_CURRENT_REVENUE: (accountId: number) =>
    `/account_plans/${accountId}/current_revenue`,
  ACCOUNT_PLANS_CURRENT_REVENUE_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/current_revenue/${id}`,

  ACCOUNT_PLANS_CURRENT_OPPORTUNITY: (accountId: number) =>
    `/account_plans/${accountId}/current_opportunity`,
  ACCOUNT_PLANS_CURRENT_OPPORTUNITY_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/current_opportunity/${id}`,

  ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY: (accountId: number) =>
    `/account_plans/${accountId}/potential_opportunity`,
  ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/potential_opportunity/${id}`,

  ACCOUNT_PLANS_REVENUE_FORECAST: (accountId: number) =>
    `/account_plans/${accountId}/revenue_forecast`,

  ACCOUNT_PLANS_REVENUE_FORECAST_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/revenue_forecast/${id}`,

  /* Account Plan Strategy API Routes */
  ACCOUNT_PLANS_MISSING_INFORMATION: (accountId: number) =>
    `/account_plans/${accountId}/missing_information`,
  ACCOUNT_PLANS_MISSING_INFORMATION_GENERATE: (accountId: number) =>
    `/account_plans/${accountId}/missing_information/generate`,
  ACCOUNT_PLANS_MISSING_INFORMATION_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/missing_information/${id}`,

  ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT: (accountId: number) =>
    `/account_plans/${accountId}/targeted_perception_development`,
  ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT_DETAIL: (
    accountId: number,
    id: number
  ) => `/account_plans/${accountId}/targeted_perception_development/${id}`,

  ACCOUNT_PLANS_ACTION_PLAN: (accountId: number) =>
    `/account_plans/${accountId}/action_plan`,
  ACCOUNT_PLANS_ACTION_PLAN_GENERATE: (accountId: number) =>
    `/account_plans/${accountId}/action_plan/generate`,
  ACCOUNT_PLANS_ACTION_PLAN_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/action_plan/${id}`,

  ACCOUNT_PLANS_TOP_ACTION: (accountId: number) =>
    `/account_plans/${accountId}/top_action`,
  ACCOUNT_PLANS_TOP_ACTION_DETAIL: (accountId: number, id: number) =>
    `/account_plans/${accountId}/top_action/${id}`,
  ACCOUNT_PLANS_ACTIVE_TOP_ACTION: "/account_plans/active_top_actions",

  ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE: (accountId: number) =>
    `/account_plans/${accountId}/client_meeting_schedule`,
  ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE_DETAIL: (
    accountId: number,
    id: number
  ) => `/account_plans/${accountId}/client_meeting_schedule/${id}`,

  LLM_CONSIDERATIONS: "/llm_considerations",
  RSSES: "/rsses",
  INDUSTRIES: "/industries",
};
