"use client";

import { useState } from "react";
import { OrganizationCrm } from "../types";
import { CirclePlus, Pencil } from "lucide-react";
import {
  <PERSON><PERSON>,
  DialogTrigger,
  <PERSON>alogTitle,
  DialogContent,
} from "@/components/ui/dialog";
import { SelectCell } from "@/components/ui/data-table/data-table-components";
import { Button } from "@/components/ui/button";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useAddOrganizationCrm } from "../api/add-organization-crm";
import { useUpdateOrganizationCrm } from "../api/update-organization-crm";
import { toast } from "sonner";

type Props = {
  isEdit?: boolean;
  organizationId: number;
  crm?: OrganizationCrm;
};

const CRM_TYPES = ["HubSpot", "Salesforce", "Microsoft Dynamics"];

const formSchema = z
  .object({
    crm_type: z.enum(["hubspot", "salesforce", "microsoft_dynamics"], {
      required_error: "CRM type is required",
    }),
    instance_url: z.string().url("Invalid URL format").optional(),
    access_token: z
      .string({ required_error: "Access token is required" })
      .min(1, "Access token is required"),
    refresh_token: z
      .string({ required_error: "Refresh token is required" })
      .min(1, "Refresh token is required"),
  })
  .superRefine((data, ctx) => {
    const needsInstanceUrl = ["salesforce", "microsoft_dynamics"].includes(
      data.crm_type
    );

    if (
      needsInstanceUrl &&
      (!data.instance_url || data.instance_url.trim() === "")
    ) {
      ctx.addIssue({
        path: ["instance_url"],
        code: z.ZodIssueCode.custom,
        message:
          "Instance URL is required for Salesforce and Microsoft Dynamics",
      });
    }
  });

type FormSchema = z.infer<typeof formSchema>;

export const OrganizationCrmForm = ({
  isEdit = false,
  crm,
  organizationId,
}: Props) => {
  const [opened, setOpen] = useState(false);
  const [selectedCRM, setSelectedCRM] = useState<string | null>(
    crm?.crm_type || null
  );

  const { mutate: addCRM } = useAddOrganizationCrm();
  const { mutate: updateCRM } = useUpdateOrganizationCrm();

  const Icon = isEdit ? Pencil : CirclePlus;

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    reValidateMode: "onSubmit",
    defaultValues: {
      crm_type: crm?.crm_type as
        | "hubspot"
        | "salesforce"
        | "microsoft_dynamics"
        | undefined,
      access_token: crm?.access_token,
      refresh_token: crm?.refresh_token,
      instance_url: crm?.instance_url ?? undefined,
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    if (isEdit)
      updateCRM(
        {
          crmId: crm?.id as number,
          orgId: organizationId,
          payload: data,
        },
        {
          onSuccess: () => {
            setOpen(false);
            form.reset();
            setSelectedCRM(null);
            toast.success("CRM updated successfully");
          },
          onError: (error) => {
            toast.error(`Failed to update CRM: ${error.message}`);
          },
        }
      );
    else
      addCRM(
        {
          orgId: organizationId,
          payload: data,
        },
        {
          onSuccess: () => {
            setOpen(false);
            form.reset();
            setSelectedCRM(null);
            toast.success("CRM added successfully");
          },
          onError: (error) => {
            toast.error(`Failed to add CRM: ${error.message}`);
          },
        }
      );
  });

  const onClose = () => {
    setOpen(false);
    form.reset();
    setSelectedCRM(null);
  };

  return (
    <>
      <Dialog open={opened} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          <Icon />
        </DialogTrigger>

        <DialogContent className="min-w-[400px] max-w-[600px]">
          <DialogTitle>{isEdit ? "Edit CRM" : "Add New CRM"}</DialogTitle>

          <Form {...form}>
            <form className="my-4 flex flex-col gap-6" onSubmit={onSubmit}>
              <FormField
                control={form.control}
                name="crm_type"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <SelectCell
                        className="text-md border-1 h-14 rounded-xl border border-gray-300 p-2 text-gray-400 active:text-black"
                        options={CRM_TYPES.map((type) => ({
                          value: type.split(" ").join("_").toLowerCase(),
                          label: type,
                        }))}
                        placeholder="Select CRM Type"
                        value={field.value}
                        onChange={(value) => {
                          field.onChange(value);
                          setSelectedCRM(value);
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {!!selectedCRM &&
                ["salesforce", "microsoft_dynamics"].includes(selectedCRM) && (
                  <FormField
                    control={form.control}
                    name="instance_url"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input
                            id="instance_url"
                            type="text"
                            error={form.formState.errors.instance_url?.message}
                            className="border-1 h-14 rounded-xl border border-gray-300 p-2"
                            placeholder="Enter instance URL"
                            {...field}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                )}

              <FormField
                control={form.control}
                name="access_token"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="access_token"
                        type="text"
                        error={form.formState.errors.access_token?.message}
                        defaultValue={crm?.access_token}
                        className="border-1 h-14 rounded-xl border border-gray-300 p-2"
                        placeholder="Enter access token"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="refresh_token"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        id="refresh_token"
                        type="text"
                        defaultValue={crm?.refresh_token}
                        error={form.formState.errors.refresh_token?.message}
                        className="border-1 h-14 rounded-xl border border-gray-300 p-2"
                        placeholder="Enter refresh token"
                        {...field}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-end gap-4">
                <Button variant="outline" onClick={onClose}>
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-gradient text-white hover:bg-primary-500"
                >
                  {isEdit ? "Update CRM" : "Add CRM"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};
