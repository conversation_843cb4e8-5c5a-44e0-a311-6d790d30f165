import { useMemo } from "react";
import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { UserManagementInvitationCodeDetailsData } from "../../types";

export const getInvitationCodeDetails = ({
  invite_code,
}: {
  invite_code: string | null;
}): ApiResponse<UserManagementInvitationCodeDetailsData[]> => {
  return api.get(API_ROUTES.USER_MANAGEMENTS_INVITATION_CODE_DETAILS, {
    params: {
      invite_code,
    },
  });
};

export const getInvitationCodeDetailsQueryOptions = (
  invite_code: string | null
) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS, invite_code],
    queryFn: () => getInvitationCodeDetails({ invite_code }),
    enabled: !!invite_code,
  });
};

type UseInvitationCodeDetailsOptions = {
  invite_code: string | null;
  queryConfig?: QueryConfig<typeof getInvitationCodeDetails>;
  options?: Partial<ReturnType<typeof getInvitationCodeDetailsQueryOptions>>;
};

export const useInvitationCodeDetails = ({
  invite_code,
  queryConfig,
  options,
}: UseInvitationCodeDetailsOptions) => {
  const invitationCodeDetailsQuery = useQuery({
    ...getInvitationCodeDetailsQueryOptions(invite_code),
    ...queryConfig,
    ...options,
  });

  const invitationCodeDetails = useMemo(() => {
    return invitationCodeDetailsQuery.data?.data;
  }, [invitationCodeDetailsQuery.data?.data]);

  return {
    ...invitationCodeDetailsQuery,
    invitationCodeDetails,
  };
};
