import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { toast } from "sonner";

export const deleteAccountPlanGroups = ({
  accountGroupId,
}: {
  accountGroupId: number;
}): ApiResponse => {
  return api.delete(API_ROUTES.ACCOUNT_PLAN_GROUPS_DETAIL(accountGroupId));
};

type UseDeleteAccountPlanGroupsOptions = {
  mutationConfig?: MutationConfig<typeof deleteAccountPlanGroups>;
};

export const useDeleteAccountPlanGroups = ({
  mutationConfig,
}: UseDeleteAccountPlanGroupsOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS],
      });

      toast("The selected account plan has been successfully removed");

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteAccountPlanGroups,
  });
};
