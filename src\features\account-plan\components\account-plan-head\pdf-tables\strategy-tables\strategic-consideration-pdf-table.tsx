import _ from "lodash";

import { PdfMarkdown, PdfTable, PdfTableRow, PdfTableTitle } from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const StrategicConsiderationPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["actionPlanList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(AccountPlanTableType.ACTION_PLAN)}
      </PdfTableTitle>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfMarkdown>{row.description}</PdfMarkdown>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
