import { useUpdateAccountPlan } from "@/features/account-plan/api/update-account-plan";
import { useParams } from "next/navigation";
import { toast } from "sonner";

const useUpdateReviewDate = () => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const updateAccountPlan = useUpdateAccountPlan({});

  const onUpdateReviewDate = async () => {
    try {
      const review_date = new Date();

      await updateAccountPlan.mutateAsync({
        accountId,
        data: {
          review_date,
        },
      });
    } catch (_) {
      toast("An error occured while updating review date");
    }
  };

  return { onUpdateReviewDate };
};

export default useUpdateReviewDate;
