import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { User, UserPayload } from "../../types/user";

export const updateUser = ({
  userId,
  data,
}: {
  userId: number;
  data?: UserPayload;
}): ApiResponse<User> => {
  return api.put(API_ROUTES.USERS_DETAIL(userId), data);
};

type UseUpdateUserOptions = {
  mutationConfig?: MutationConfig<typeof updateUser>;
};

export const useUpdateUser = ({ mutationConfig }: UseUpdateUserOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.USER],
        });

        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.USER_MANAGEMENTS],
        });
      }

      onSuccess?.(...args);
    },
    mutationFn: updateUser,
    ...restConfig,
  });
};
