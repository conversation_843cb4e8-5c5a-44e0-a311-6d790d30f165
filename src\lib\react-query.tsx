/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { SortColumn, SortDirection } from "@/types/api";
import {
  UseMutationOptions,
  QueryClient,
  QueryCache,
} from "@tanstack/react-query";
import React from "react";

const persister = createSyncStoragePersister({
  storage: typeof window !== "undefined" ? window.localStorage : undefined,
});

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: false,
      gcTime: 1000 * 60 * 5, // 5 minutes,
    },
  },
});

export const QueryClientProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return (
    <PersistQueryClientProvider
      client={queryClient}
      persistOptions={{ persister }}
    >
      {children}
    </PersistQueryClientProvider>
  );
};

export type ApiFnReturnType<FnType extends (...args: any) => Promise<any>> =
  Awaited<ReturnType<FnType>>;

export type QueryConfig<T extends (...args: any[]) => any> = Omit<
  ReturnType<T>,
  "queryKey" | "queryFn"
>;

export type MutationConfig<
  MutationFnType extends (...args: any) => Promise<any>,
> = UseMutationOptions<
  ApiFnReturnType<MutationFnType>,
  Error,
  Parameters<MutationFnType>[0]
> & { invalidate?: boolean; notification?: boolean };

export type ApiResponse<T = undefined, TOptions = unknown> = Promise<
  {
    data: T;
    pagination: {
      current_page: number | null;
      next_page: number | null;
      prev_page: number | null;
      total_pages: number | null;
      total_count: number | null;
      size: number | null;
    };
  } & TOptions
>;

export type BaseParams = {
  sort_column?: SortColumn;
  sort_direction?: SortDirection;
  search?: string;
  disable_pagination?: boolean;
};

export const queryCache = new QueryCache({
  onError: (error) => {
    console.log(error);
  },
  onSuccess: (data) => {
    console.log(data);
  },
  onSettled: (data, error) => {
    console.log(data, error);
  },
});
