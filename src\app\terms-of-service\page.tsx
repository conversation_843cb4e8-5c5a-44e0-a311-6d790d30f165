"use client";

import withNotLoginRoute from "@/features/auth/components/not-login-route";
import {
  UserAgreementItem,
  UserAgreementList,
  UserAgreementsLayout,
} from "@/features/landing-page/user-agreements-layout";

function TermsOfService() {
  return (
    <UserAgreementsLayout>
      <h1 className="text-3xl font-semibold">Terms of Service</h1>
      <p className="mt-4">
        Welcome to <strong>Perception Selling.ai.</strong> These Terms of
        Service govern your access to and use of our software-as-a-service
        platform. By using our services, you agree to the following:
      </p>

      <div className="mt-8 space-y-6">
        <UserAgreementItem title="1. Service Overview">
          <p>
            Our platform helps B2B account managers uncover strategic
            opportunities through structured analysis and AI-supported
            workflows.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="2. User Responsibilities">
          <p>You agree to:</p>

          <UserAgreementList>
            <li>Provide accurate and complete information</li>
            <li>Use the service only for lawful purposes</li>
            <li>Protect your login credentials from misuse</li>
            <li>
              Ensure that any data submitted complies with your company’s
              privacy obligations
            </li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="3. Data Ownership and Confidentiality">
          <UserAgreementList>
            <li>
              You retain full ownership of all data you upload, including
              employee, client, and strategy information
            </li>
            <li>
              We treat your data as <strong>strictly confidential</strong>
            </li>
            <li>
              We do{" "}
              <strong>
                not use, replicate, or disclose your proprietary data
              </strong>{" "}
              for purposes outside platform delivery
            </li>
          </UserAgreementList>
          <p className="mt-4">
            Prohibited data includes (but is not limited to) reselling or using:
          </p>

          <UserAgreementList>
            <li>Employee names or emails</li>
            <li>Client stakeholder data</li>
            <li>SWOT or strategic inputs</li>
            <li>Internal revenue data</li>
            <li>Meeting schedules</li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="4. Use of AI and LLMs">
          <p>
            Perception Selling.ai may utilize third-party LLMs to{" "}
            <strong>process anonymized, non-financial strategic data.</strong>
          </p>
          <p>You acknowledge that:</p>

          <UserAgreementList>
            <li>No revenue data is submitted into LLMs</li>
            <li>
              LLM use is limited to functions such as account mapping and
              strategic insight generation
            </li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="5. Platform Access and Availability">
          <p>
            We strive to ensure 99.9% uptime but do not guarantee uninterrupted
            service. Access may occasionally be disrupted for maintenance or
            upgrades.
          </p>
        </UserAgreementItem>

        <UserAgreementItem title="6. Limitations of Liability">
          <p>We are not liable for:</p>
          <UserAgreementList>
            <li>
              Decisions or business outcomes based on our platform’s
              recommendations
            </li>
            <li>Data loss due to user-side mismanagement</li>
            <li>Indirect or consequential damages from use of our service</li>
          </UserAgreementList>
        </UserAgreementItem>

        <UserAgreementItem title="7. Amendments">
          <p>
            We may update these Terms periodically. Continued use after updates
            constitutes acceptance. Material changes will be communicated in
            advance.
          </p>
        </UserAgreementItem>
      </div>
    </UserAgreementsLayout>
  );
}

export default withNotLoginRoute(TermsOfService);
