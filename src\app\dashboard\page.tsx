"use client";

import React from "react";

import { useIsHydrated } from "@/lib/hooks/use-hydrated";
import SearchAccountPlan from "@/features/account-plan/components/search-account-plan";
import AccountPlanChart from "@/features/account-plan/components/account-plan-dashboard/revenue-charts";
import { NewsSection } from "@/features/account-plan/components/account-plan-dashboard/news-sections";
import { AllStrategicActionsTable } from "@/features/account-plan/components/account-plan-dashboard/all-strategic-actions-table";

function DashboardPage() {
  const { isHydrated } = useIsHydrated();

  if (!isHydrated) return null;

  return (
    <div className="w-full">
      <div className="mb-8 flex justify-between">
        <h1 className="text-3xl font-bold text-primary-500">Dashboard</h1>
        <SearchAccountPlan />
      </div>

      <div className="flex h-[50vh] gap-8">
        <AccountPlanChart />
        <NewsSection />
      </div>

      <AllStrategicActionsTable />
    </div>
  );
}

export default DashboardPage;
