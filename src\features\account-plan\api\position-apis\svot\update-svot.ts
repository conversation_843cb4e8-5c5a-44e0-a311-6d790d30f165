import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APSvot,
  APSvotBaseData,
} from "@/features/account-plan/types/position-types";

export const updateSvot = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APSvotBaseData;
}): ApiResponse<APSvot> => {
  return api.put(API_ROUTES.ACCOUNT_PLANS_SVOT_DETAIL(accountId, id), data);
};

type UseUpdateSvotOptions = {
  mutationConfig?: MutationConfig<typeof updateSvot>;
};

export const useUpdateSvot = ({ mutationConfig }: UseUpdateSvotOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_SVOT,
        ],
      });

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateSvot,
  });
};
