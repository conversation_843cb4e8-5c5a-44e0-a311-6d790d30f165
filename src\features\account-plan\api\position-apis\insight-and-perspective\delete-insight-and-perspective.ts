import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const deleteInsightAndPerspective = ({
  accountId,
  id,
}: {
  id: number;
  accountId: number;
}): ApiResponse => {
  return api.delete(
    API_ROUTES.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE_DETAIL(accountId, id)
  );
};

type UseDeleteInsightAndPerspectiveOptions = {
  mutationConfig?: MutationConfig<typeof deleteInsightAndPerspective>;
};

export const useDeleteInsightAndPerspective = ({
  mutationConfig,
}: UseDeleteInsightAndPerspectiveOptions) => {
  const queryClient = useQueryClient();

  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_INSIGHT_AND_PERSPECTIVE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: deleteInsightAndPerspective,
  });
};
