import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APClientMeetingSchedule } from "@/features/account-plan/types/strategy-types";

type ClientMeetingScheduleListParams = BaseParams;

export const getClientMeetingScheduleList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: ClientMeetingScheduleListParams;
}): ApiResponse<APClientMeetingSchedule[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE(accountId), {
    params,
  });
};

export const getClientMeetingScheduleListQueryOptions = (
  accountId: number,
  params?: ClientMeetingScheduleListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE,
    ],
    queryFn: () => getClientMeetingScheduleList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseClientMeetingScheduleListOptions = {
  params?: ClientMeetingScheduleListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getClientMeetingScheduleList>;
  options?: Partial<
    ReturnType<typeof getClientMeetingScheduleListQueryOptions>
  >;
};

export const useClientMeetingScheduleList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseClientMeetingScheduleListOptions) => {
  const clientMeetingScheduleListQuery = useQuery({
    ...getClientMeetingScheduleListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...clientMeetingScheduleListQuery,
    clientMeetingScheduleList: clientMeetingScheduleListQuery.data?.data,
  };
};
