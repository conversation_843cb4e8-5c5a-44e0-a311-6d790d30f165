import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APSvot } from "@/features/account-plan/types/position-types";

export const getSvotDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APSvot> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_SVOT_DETAIL(accountId, id));
};

export const getSvotDetailQueryOptions = (accountId: number, id: number) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_SVOT,
      id,
    ],
    queryFn: () => getSvotDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseSvotDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getSvotDetail>;
  options?: Partial<ReturnType<typeof getSvotDetailQueryOptions>>;
};

export const useSvotDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseSvotDetailOptions) => {
  const svotDetailQuery = useQuery({
    ...getSvotDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...svotDetailQuery,
    svotDetail: svotDetailQuery.data?.data,
  };
};
