"use client";

import { PATH } from "@/constants/path";
import { QUERY_KEYS } from "@/constants/query-keys";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useAuthStore } from "../stores/auth-store";

export const useLogout = () => {
  const queryClient = useQueryClient();
  const { resetToken } = useAuthStore();
  const router = useRouter();

  const logout = () => {
    router.push(PATH.LANDING);

    setTimeout(() => {
      resetToken();
    }, 0);

    queryClient.removeQueries({ queryKey: [QUERY_KEYS.AUTH_INFO] });
  };

  return logout;
};
