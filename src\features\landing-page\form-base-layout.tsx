import React from "react";
import Image from "next/image";

import PsAiTextLogo from "@/assets/ps-ai-color-logo.png";
import { cn } from "@/lib/utils";

export function FormHeader() {
  return (
    <div className="flex items-center justify-center">
      <Image
        src={PsAiTextLogo}
        alt="PS-AI-Text-Logo"
        width={200}
        height={140}
      />
    </div>
  );
}

export function FormContainer({
  children,
  className,
  ...props
}: {
  children: React.ReactNode;
} & React.HTMLAttributes<HTMLFormElement>) {
  return (
    <form
      className={cn(
        "flex max-w-[650px] grow rounded-xl !bg-white p-8 text-primary-500 shadow-[3px_0_14px_8px_rgba(0,0,0,0.25)]",
        className
      )}
      {...props}
    >
      <div className="mx-auto my-auto flex w-full max-w-md flex-col gap-4 px-4 xl:max-w-[20vw]">
        <FormHeader />
        {children}
      </div>
    </form>
  );
}
