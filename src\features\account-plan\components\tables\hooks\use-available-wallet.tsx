import { useMemo } from "react";
import { useParams } from "next/navigation";
import { useWalletShareList } from "@/features/account-plan/api/position-apis/wallet-share/get-wallet-share-list";
import { APWalletShareType } from "@/features/account-plan/types/position-types";

import { useCurrentOpportunityList } from "@/features/account-plan/api/revenue-apis/current-opportunity/get-current-opportunity-list";
import { usePotentialOpportunityList } from "@/features/account-plan/api/revenue-apis/potential-opportunity/get-potential-opportunity-list";
import { useCurrentRevenueList } from "@/features/account-plan/api/revenue-apis/current-revenue/get-current-revenue-list";

export const useAvailableWalletSize = () => {
  const { id } = useParams<{ id: string }>();
  const accountId = parseInt(id);

  const { walletShareList } = useWalletShareList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const { currentRevenueList } = useCurrentRevenueList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const { currentOpportunityList } = useCurrentOpportunityList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const { potentialOpportunityList } = usePotentialOpportunityList({
    accountId,
    params: {
      disable_pagination: true,
    },
  });

  const totalCurrentRevenue = useMemo(() => {
    return currentRevenueList?.reduce((acc, curr) => acc + curr.value, 0) ?? 0;
  }, [currentRevenueList]);

  const totalCurrentOpportunity = useMemo(() => {
    return (
      currentOpportunityList?.reduce((acc, curr) => acc + curr.value, 0) ?? 0
    );
  }, [currentOpportunityList]);

  const totalPotentialOpportunity = useMemo(() => {
    return (
      potentialOpportunityList?.reduce((acc, curr) => acc + curr.value, 0) ?? 0
    );
  }, [potentialOpportunityList]);

  const walletSize = useMemo(
    () =>
      walletShareList?.find(
        (v) => v.item_type === APWalletShareType.ADDRESSABLE
      )?.shared_type_analysis ?? 0,
    [walletShareList]
  );

  const existingWallet = useMemo(
    () =>
      walletShareList?.find((v) => v.item_type === APWalletShareType.OURS)
        ?.shared_type_analysis ?? 0,
    [walletShareList]
  );

  const competitionWallet = useMemo(
    () =>
      walletShareList?.find(
        (v) => v.item_type === APWalletShareType.COMPETITION
      )?.shared_type_analysis ?? 0,
    [walletShareList]
  );

  const availableWallet =
    walletSize -
    existingWallet -
    competitionWallet -
    totalCurrentOpportunity -
    totalPotentialOpportunity;

  return {
    currentRevenueList,
    availableWallet: availableWallet > 0 ? availableWallet : 0,
    totalCurrentRevenue,
    totalCurrentOpportunity,
    totalPotentialOpportunity,
    totalOpportunityWallet: totalCurrentOpportunity + totalPotentialOpportunity,
  };
};
