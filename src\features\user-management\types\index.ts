import { UserRole } from "@/features/auth/types/user";

/* User Management Types */
export type UserManagement = {
  user_id: number;
  first_name: string | null;
  last_name: string | null;
  email: string;
  contact: string | null;
  role: {
    created_at: string | null;
    discarded_at: string | null;
    id: number;
    name: UserRole;
    organization_id: number;
    updated_at: string | null;
  } | null;
  company_name: string | null;
  prompt_tuning_permissions: boolean;
  country_code: string | null;
  organization: {
    id: number;
    name: string;
  };
  org_employee_id: string;
  team_leader: {
    first_name: string | null;
    last_name: string | null;
  } | null;
  last_login_at: string;
  organization_unique_id: string;
  organization_identifier_id: string;
};

/* User Management Invite Types */
export type UserManagementInviteBaseData = {
  email: string;
  role: UserRole;
};

export type UserManagementInviteData = {
  email: string;
  invitation_expiry_date: string;
  invitation_status: string;
};

/* User Management Accept Invitation Types */
export type UserManagementAcceptInvitationBaseData = {
  invite_code: string;
  password: string;
  name: string;
  last_name?: string;
};

export type UserManagementAcceptInvitationData = {
  id: number;
  first_name: string | null;
  last_name: string | null;
  photo_url: string | null;
  email: string;
  contact: string | null;
  last_login_at: string | null;
  organization_unique_id: string;
};

/* User Management Invitation Code Types */
export type UserManagementInvitationCodeDetailsData = {
  email: string;
  invitation_status: string;
  invitation_expiry_date: string;
  invitation_code: string;
  organization: {
    id: number;
    name: string;
  };
  invited_by: {
    id: number;
    first_name: string | null;
    last_name: string | null;
  };
};

type Permissions = {
  prompt_tuning_permissions: boolean;
};

/* User Management User Permissions Types */
export type UserPermissionsPayload = {
  permissions: Partial<Permissions>;
};

export type UserPermissions = {
  roles: Permissions;
};

/* User Management - Password Types */
export type ForgotPasswordPayload = {
  email: string;
};

export type ChangePasswordPayload = {
  request_code: string;
  password: string;
  password_confirmation: string;
};
