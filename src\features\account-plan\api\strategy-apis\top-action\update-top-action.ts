import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APTopAction,
  APTopActionBaseData,
} from "@/features/account-plan/types/strategy-types";

export const updateTopAction = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APTopActionBaseData;
}): ApiResponse<APTopAction> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_TOP_ACTION_DETAIL(accountId, id),
    data
  );
};

type UseUpdateTopActionOptions = {
  mutationConfig?: MutationConfig<typeof updateTopAction>;
};

export const useUpdateTopAction = ({
  mutationConfig,
}: UseUpdateTopActionOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_TOP_ACTION,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateTopAction,
  });
};
