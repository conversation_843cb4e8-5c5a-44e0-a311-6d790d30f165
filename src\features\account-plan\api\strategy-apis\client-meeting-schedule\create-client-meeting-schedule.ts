import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APClientMeetingSchedule,
  APClientMeetingScheduleBaseData,
} from "@/features/account-plan/types/strategy-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createClientMeetingSchedule = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APClientMeetingScheduleBaseData;
}): ApiResponse<APClientMeetingSchedule> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE(accountId),
    data
  );
};

type UseCreateClientMeetingScheduleOptions = {
  mutationConfig?: MutationConfig<typeof createClientMeetingSchedule>;
};

export const useCreateClientMeetingSchedule = ({
  mutationConfig,
}: UseCreateClientMeetingScheduleOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_CLIENT_MEETING_SCHEDULE,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createClientMeetingSchedule,
  });
};
