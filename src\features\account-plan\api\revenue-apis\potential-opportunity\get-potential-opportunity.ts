import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APPotentialOpportunity } from "@/features/account-plan/types/revenue-types";

export const getPotentialOpportunityDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APPotentialOpportunity> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY_DETAIL(accountId, id)
  );
};

export const getPotentialOpportunityDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_POTENTIAL_OPPORTUNITY,
      id,
    ],
    queryFn: () => getPotentialOpportunityDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UsePotentialOpportunityDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getPotentialOpportunityDetail>;
  options?: Partial<
    ReturnType<typeof getPotentialOpportunityDetailQueryOptions>
  >;
};

export const usePotentialOpportunityDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UsePotentialOpportunityDetailOptions) => {
  const potentialOpportunityDetailQuery = useQuery({
    ...getPotentialOpportunityDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...potentialOpportunityDetailQuery,
    potentialOpportunityDetail: potentialOpportunityDetailQuery.data?.data,
  };
};
