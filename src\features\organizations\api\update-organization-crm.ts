import { api } from "@/lib/api-client";
import { useMutation } from "@tanstack/react-query";
import { OrganizationCrm } from "../types";

export const updateOrganizationCrm = (data: {
  orgId: number;
  crmId: number;
  payload: Partial<OrganizationCrm>;
}) => api.put(`/organizations/${data.orgId}/crms/${data.crmId}`, data.payload);

export const useUpdateOrganizationCrm = () =>
  useMutation({ mutationFn: updateOrganizationCrm });
