import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  ListTemplateCategory,
  ModelTemplate,
  ModelTemplateStatus,
} from "../types";

type ModelListParams = {
  list_template_ap_category?: ListTemplateCategory;
  category?: string;
  organization_prompt?: boolean;
  draft?: boolean;
  status?: ModelTemplateStatus;
} & BaseParams;

export const getModelList = ({
  params,
}: {
  params?: ModelListParams;
}): ApiResponse<ModelTemplate[]> => {
  return api.get(API_ROUTES.MODEL_TEMPLATES, { params });
};

export const getModelListQueryOptions = (params?: ModelListParams) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.MODEL_TEMPLATES, params].filter((v) => !!v),
    queryFn: () => getModelList({ params }),
  });
};

type UseModelListOptions = {
  params?: ModelListParams;
  queryConfig?: QueryConfig<typeof getModelList>;
};

export const useModelList = ({ queryConfig, params }: UseModelListOptions) => {
  const modelListQuery = useQuery({
    ...getModelListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...modelListQuery,
    modelList: modelListQuery.data?.data || [],
  };
};
