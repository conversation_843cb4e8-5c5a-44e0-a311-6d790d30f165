import _ from "lodash";

import { PdfMarkdown, PdfTable, PdfTableRow, PdfTableTitle } from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const InformationNeededPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["missingInformationList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.MISSING_INFORMATION)}
      </PdfTableTitle>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfMarkdown>{row.description}</PdfMarkdown>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
