import { TablerIcon } from "@tabler/icons-react";
import * as React from "react";

import { cn } from "@/lib/utils";

export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  startIcon?: TablerIcon;
  endIcon?: TablerIcon;
  iconProps?: React.ComponentProps<TablerIcon>;
  error?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, startIcon, endIcon, iconProps, ...props }, ref) => {
    const StartIcon = startIcon;
    const EndIcon = endIcon;

    if (!startIcon && !endIcon)
      return (
        <>
          <input
            type={type}
            className={cn(
              "flex h-[4.5vh] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm text-neutral-950 ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-neutral-950 placeholder:text-neutral-500 focus-visible:border-primary-500 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-neutral-800 dark:bg-neutral-950 dark:ring-offset-neutral-950 dark:file:text-neutral-50 dark:placeholder:text-neutral-400 dark:focus-visible:ring-neutral-300",
              startIcon ? "pl-10" : "",
              endIcon ? "pr-10" : "",
              className
            )}
            ref={ref}
            {...props}
          />
          {props.error && (
            <p className="mt-1 text-xs text-red-500">{props.error}</p>
          )}
        </>
      );

    return (
      <div className={cn("relative")}>
        {StartIcon && (
          <div className="absolute left-2 top-1/2 -translate-y-1/2 transform">
            <StartIcon
              size={18}
              className="text-muted-foreground"
              {...iconProps}
            />
          </div>
        )}
        <input
          type={type}
          className={cn(
            "flex h-[4.5vh] w-full rounded-md border border-neutral-200 bg-white px-3 py-2 text-sm text-neutral-950 ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-neutral-950 placeholder:text-neutral-500 focus-visible:border-primary-500 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-neutral-800 dark:bg-neutral-950 dark:ring-offset-neutral-950 dark:file:text-neutral-50 dark:placeholder:text-neutral-400 dark:focus-visible:ring-neutral-300",
            startIcon ? "pl-10" : "",
            endIcon ? "pr-10" : "",
            className
          )}
          ref={ref}
          {...props}
        />
        {props.error && (
          <p className="mt-1 text-xs text-red-500">{props.error}</p>
        )}
        {EndIcon && (
          <div className="absolute right-3 top-1/2 -translate-y-1/2 transform">
            <EndIcon
              size={18}
              {...iconProps}
              className={cn("text-muted-foreground", iconProps?.className)}
            />
          </div>
        )}
      </div>
    );
  }
);
Input.displayName = "Input";

export { Input };
