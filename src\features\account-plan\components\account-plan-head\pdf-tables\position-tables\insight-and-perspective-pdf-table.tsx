import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "../";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const InsightsAndPerspectivesPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["insightAndPerspectiveList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(AccountPlanTableType.INSIGHT_AND_PERSPECTIVE)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader style={{ flex: 4 }}>Description</PdfTableHeader>
        <PdfTableHeader>Target Name</PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell style={{ flex: 4 }}>{row.description}</PdfTableCell>
          <PdfTableCell>{row.ap_stakeholder_mapping_item?.name}</PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
