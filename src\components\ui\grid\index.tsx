"use client";

import React, { ForwardedRef } from "react";
import { AgGridReact } from "ag-grid-react";
import "ag-grid-community/styles/ag-grid.css";
import "ag-grid-community/styles/ag-theme-quartz.css";
import { cn } from "@/lib/utils";
import { cva, VariantProps } from "class-variance-authority";
import "./grid-style.css";

const gridVariants = cva("ag-theme-quartz", {
  variants: {
    variant: {
      "alt1-secondary": "table-style-alt-1 table-style-alt-1-secondary",
      alt1: "table-style-alt-1",
      default: "table-style-default",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

export type GridProps<T> = React.ComponentProps<typeof AgGridReact<T>> &
  VariantProps<typeof gridVariants> & { height?: string };
export type GridRef<T> = ForwardedRef<React.ElementRef<typeof AgGridReact<T>>>;

function GridComp<T>(
  {
    variant,
    height = "75vh",
    className,
    suppressCellFocus,
    rowClass,
    ...props
  }: GridProps<T>,
  ref: GridRef<T>
) {
  return (
    <div
      className={cn("ag-theme-quartz", gridVariants({ variant }), className)}
      style={{ height }}
    >
      <AgGridReact<T>
        ref={ref}
        suppressRowTransform
        suppressMovableColumns
        stopEditingWhenCellsLoseFocus
        suppressDragLeaveHidesColumns
        singleClickEdit
        suppressCellFocus={suppressCellFocus || !!props.onRowClicked}
        rowClass={cn(!!props.onRowClicked && "cursor-pointer", rowClass)}
        {...props}
      />
    </div>
  );
}

export const Grid = React.forwardRef(GridComp) as <T extends {}>(
  props: GridProps<T> & {
    ref?: GridRef<T>;
  }
) => ReturnType<typeof GridComp>;
