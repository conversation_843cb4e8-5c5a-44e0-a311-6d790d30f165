import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { ModelTemplate, ModelTemplateBaseData } from "../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const updateModelDetail = ({
  modelId,
  data,
}: {
  modelId: number;
  data?: Partial<ModelTemplateBaseData>;
}): ApiResponse<ModelTemplate> => {
  return api.put(`${API_ROUTES.MODEL_TEMPLATES}/${modelId}`, data);
};

type UseUpdateModelDetailOptions = {
  mutationConfig?: MutationConfig<typeof updateModelDetail>;
};

export const useUpdateModelDetail = ({
  mutationConfig,
}: UseUpdateModelDetailOptions) => {
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      if (invalidate) {
        queryClient.invalidateQueries({
          queryKey: [QUERY_KEYS.MODEL_TEMPLATES],
        });
      }

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: updateModelDetail,
  });
};
