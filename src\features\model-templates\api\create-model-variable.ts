import { useMutation } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { ModelVariable } from "../types";

type CreateModelVariableBaseData = Omit<
  ModelVariable,
  "id" | "variable_reference"
>;

export type CreateModelVariableData = Partial<CreateModelVariableBaseData> & {
  variable_reference_url?: string | null;
};

export const createModelVariable = ({
  data,
}: {
  data?: CreateModelVariableData;
}): ApiResponse<ModelVariable> => {
  return api.post(API_ROUTES.MODEL_TEMPLATES_VARIABLES, data);
};

type UseCreateModelVariableOptions = {
  mutationConfig?: MutationConfig<typeof createModelVariable>;
};

export const useCreateModelVariable = ({
  mutationConfig,
}: UseCreateModelVariableOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createModelVariable,
  });
};
