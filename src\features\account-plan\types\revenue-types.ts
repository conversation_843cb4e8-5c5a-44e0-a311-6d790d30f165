/* Account Plan Historic Revenue */
export type APHistoricRevenueBaseData = Partial<Omit<APHistoricRevenue, "id">>;

export type APHistoricRevenue = {
  id: number;
  time_month: string;
  product_service_name: string;
  value: number;
  currency: string;
};

/* Account Plan existing revenue */
export type APCurrentRevenueBaseData = Partial<Omit<APCurrentRevenue, "id">>;

export type APCurrentRevenue = {
  id: number;
  renewal_date: string | null;
  product_service_name: string;
  value: number;
  currency: string;
};

/* Account Plan Current Opportunity */
export type APCurrentOpportunityBaseData = Partial<
  Omit<APCurrentOpportunity, "id">
>;

export type APCurrentOpportunity = {
  id: number;
  close_date: string | null;
  product_service_name: string;
  value: number;
  currency: string;
};

/* Account Plan Potential Opportunity */
export type APPotentialOpportunityBaseData = Partial<
  Omit<APPotentialOpportunity, "id">
>;

export type APPotentialOpportunity = {
  id: number;
  close_date: string | null;
  product_service_name: string;
  value: number;
  currency: string;
};

/* Account Plan Revenue Forecast */
export type APRevenueForecastBaseData = Partial<Omit<APRevenueForecast, "id">>;

export type APRevenueForecast = {
  id: number;
  timespan: string;
  low_scenario: number | null;
  realistic_scenario: number | null;
  high_scenario: number | null;
};
