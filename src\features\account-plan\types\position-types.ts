/* Account Plan Stakeholder Mapping */
export enum APStakeholderInfluence {
  LOW = "Low",
  MEDIUM = "Medium",
  HIGH = "High",
}

export enum APStakeholderRole {
  SPONSOR = "Sponsor",
  SPONSOR_COACH = "Sponsor & Coach",
  COACH = "Coach",
  ANTI_SPONSOR = "Anti-sponsor",
  STAKEHOLDER = "Stakeholder",
}

export enum APStakeholderAdvocacy {
  ADVOCATES_US = "Advocates Us",
  PREFERS_US = "Prefers Us",
  NO_PREFERENCE = "No Preference",
  UNSURE_OF_US = "Unsure of Us",
  PREFERS_ALTERNATIVE = "Prefers Alternatives",
  ADVOCATES_ALTERNATIVE = "Advocates Alternatives",
}

export enum APStakeholderPerception {
  STRATEGIC_ADVISOR = "Strategic Advisor",
  TRUSTED_PARTNER = "Trusted Partner",
  SOLUTION_PROVIDER = "Solution Provider",
  PREFERRED_SUPPLIER = "Preferred Supplier",
  VENDOR = "Vendor",
}

export type APStakeholderMappingBaseData = Partial<
  Omit<APStakeholderMapping, "id">
>;

export type APStakeholderMapping = {
  id: number;
  name: string | null;
  job_title: string | null;
  location: string | null;
  influence: APStakeholderInfluence | null;
  role: string[] | null;
  perception: string | null;
  advocacy: string | null;
  coverage: boolean | null;
};

/* Account Plan Wallet Share */
export enum APWalletShareType {
  ADDRESSABLE = "addressable",
  OURS = "ours",
  COMPETITION = "competition",
  AVAILABLE = "available",
}

export type APWalletShareBaseData = Partial<Omit<APWalletShare, "id">>;

export type APWalletShare = {
  id: number;
  shared_type_analysis: number | null;
  currency: string;
  item_type: APWalletShareType;
  product_service_name: string;
  description: string;
};

/* Account Plan Circumstantial Analysis */
export enum APCircumstantialAnalysisType {
  MACRO = "macro",
  INDUSTRY = "industry",
  BUSINESS = "business",
}

export type APCircumstantialAnalysisBaseData = Partial<
  Omit<APCircumstantialAnalysis, "id">
>;

export type APCircumstantialAnalysis = {
  id: number;
  item_type: APCircumstantialAnalysisType;
  description: string;
};

/* Account Plan SVOT Analysis */
export enum APSvotType {
  STRENGTH = "strength",
  VULNERABILITY = "vulnerability",
  OPPORTUNITY = "opportunity",
  THREAT = "threat",
}

export type APSvotBaseData = Partial<Omit<APSvot, "id">>;

export type APSvot = {
  id: number;
  item_type: APSvotType;
  description: string;
};

/* Account Plan Insight and Perspective Items */
export type APInsightAndPerspectiveBaseData = Partial<{
  ap_stakeholder_mapping_item_id: number;
}>;

export type APInsightAndPerspective = {
  id: number;
  ap_stakeholder_mapping_item: APStakeholderMapping | null;
  description: string;
};
