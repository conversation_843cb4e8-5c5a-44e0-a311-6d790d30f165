import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";

export const generateActionPlan = ({
  accountId,
}: {
  accountId: number;
}): ApiResponse => {
  return api.post(API_ROUTES.ACCOUNT_PLANS_ACTION_PLAN_GENERATE(accountId));
};

type UseGenerateActionPlanOptions = {
  mutationConfig?: MutationConfig<typeof generateActionPlan>;
};

export const useGenerateActionPlan = ({
  mutationConfig,
}: UseGenerateActionPlanOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_ACTION_PLAN,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: generateActionPlan,
  });
};
