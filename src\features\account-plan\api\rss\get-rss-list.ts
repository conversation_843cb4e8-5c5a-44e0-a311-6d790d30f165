import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { RSSData } from "../../types/rss-types";

type RssListParams = BaseParams & { industry_id?: number; ap_active?: boolean };

export const getRssList = ({
  params,
}: {
  params?: RssListParams;
}): ApiResponse<RSSData[]> => {
  return api.get(API_ROUTES.RSSES, { params });
};

export const getRssListQueryOptions = (params?: RssListParams) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.RSSES, params],
    queryFn: () => getRssList({ params }),
  });
};

type UseRssOptions = {
  params?: RssListParams;
  queryConfig?: QueryConfig<typeof getRssList>;
};

export const useRssList = ({ queryConfig, params }: UseRssOptions) => {
  const rssListQuery = useQuery({
    ...getRssListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...rssListQuery,
    rssList: rssListQuery.data?.data || [],
  };
};
