import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APWalletShare } from "@/features/account-plan/types/position-types";

export const getWalletShareDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APWalletShare> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_WALLET_SHARE_DETAIL(accountId, id));
};

export const getWalletShareDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_WALLET_SHARE,
      id,
    ],
    queryFn: () => getWalletShareDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseWalletShareDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getWalletShareDetail>;
  options?: Partial<ReturnType<typeof getWalletShareDetailQueryOptions>>;
};

export const useWalletShareDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseWalletShareDetailOptions) => {
  const walletShareDetailQuery = useQuery({
    ...getWalletShareDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...walletShareDetailQuery,
    walletShareDetail: walletShareDetailQuery.data?.data,
  };
};
