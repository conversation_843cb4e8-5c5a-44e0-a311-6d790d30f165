import { IconChevronDown } from "@tabler/icons-react";
import { ICellRendererParams } from "ag-grid-community";
import React from "react";

export const formatDecimalValue = (params: {
  value: number | null | undefined;
}): string => {
  const { value } = params;
  if (value === null || value === undefined) {
    return ""; // Return empty string for null or undefined
  }
  return new Intl.NumberFormat().format(value); // Format the number
};

export const renderSelectWithCaret = (params: ICellRendererParams) => {
  const value = params.value || "";

  return (
    <div className="flex w-full items-center justify-between">
      {value} <IconChevronDown className="ml-auto" />
    </div>
  );
};

export const getEmptyTableOverlay = (copy: React.ReactNode) => {
  return (
    <div className="align-center flex flex-col text-neutral-300">
      <p className="text-wrap text-3xl">{copy}</p>
    </div>
  );
};
