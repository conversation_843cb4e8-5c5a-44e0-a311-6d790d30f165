import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { AccountPlanGroupsData, AccountPlanGroupsPayload } from "../../types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createAccountPlanGroup = ({
  data,
}: {
  data?: AccountPlanGroupsPayload;
}): ApiResponse<AccountPlanGroupsData> => {
  return api.post(`${API_ROUTES.ACCOUNT_PLAN_GROUPS}`, data);
};

type UseCreateAccountPlanGroupOptions = {
  mutationConfig?: MutationConfig<typeof createAccountPlanGroup>;
};

export const useCreateAccountPlanGroup = ({
  mutationConfig,
}: UseCreateAccountPlanGroupOptions) => {
  const { onSuccess, ...restConfig } = mutationConfig || {};
  const queryClient = useQueryClient();

  return useMutation({
    onSuccess: (...args) => {
      onSuccess?.(...args);

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEYS.ACCOUNT_PLANS_GROUPS],
      });
    },
    ...restConfig,
    mutationFn: createAccountPlanGroup,
  });
};
