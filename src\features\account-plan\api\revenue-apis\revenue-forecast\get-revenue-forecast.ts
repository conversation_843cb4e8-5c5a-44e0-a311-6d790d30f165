import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APRevenueForecast } from "@/features/account-plan/types/revenue-types";

export const getRevenueForecastDetail = ({
  id,
  accountId,
}: {
  accountId: number;
  id: number;
}): ApiResponse<APRevenueForecast> => {
  return api.get(
    API_ROUTES.ACCOUNT_PLANS_REVENUE_FORECAST_DETAIL(accountId, id)
  );
};

export const getRevenueForecastDetailQueryOptions = (
  accountId: number,
  id: number
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_REVENUE_FORECAST,
      id,
    ],
    queryFn: () => getRevenueForecastDetail({ accountId, id }),
    enabled: !!accountId && !!id,
  });
};

type UseRevenueForecastDetailOptions = {
  id: number;
  accountId: number;
  queryConfig?: QueryConfig<typeof getRevenueForecastDetail>;
  options?: Partial<ReturnType<typeof getRevenueForecastDetailQueryOptions>>;
};

export const useRevenueForecastDetail = ({
  id,
  accountId,
  queryConfig,
  options,
}: UseRevenueForecastDetailOptions) => {
  const revenueForecastDetailQuery = useQuery({
    ...getRevenueForecastDetailQueryOptions(accountId, id),
    ...queryConfig,
    ...options,
  });

  return {
    ...revenueForecastDetailQuery,
    revenueForecastDetail: revenueForecastDetailQuery.data?.data,
  };
};
