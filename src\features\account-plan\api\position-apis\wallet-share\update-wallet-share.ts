import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import {
  APWalletShare,
  APWalletShareBaseData,
} from "@/features/account-plan/types/position-types";

export const updateWalletShare = ({
  accountId,
  id,
  data,
}: {
  id: number;
  accountId: number;
  data?: APWalletShareBaseData;
}): ApiResponse<APWalletShare> => {
  return api.put(
    API_ROUTES.ACCOUNT_PLANS_WALLET_SHARE_DETAIL(accountId, id),
    data
  );
};

type UseUpdateWalletShareOptions = {
  mutationConfig?: MutationConfig<typeof updateWalletShare>;
};

export const useUpdateWalletShare = ({
  mutationConfig,
}: UseUpdateWalletShareOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_WALLET_SHARE,
          ],
        });
      }

      onSuccess?.(...args);
    },

    ...restConfig,
    mutationFn: updateWalletShare,
  });
};
