import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { formatDate } from "@/lib/utils";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const MeetingSchedulePDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["clientMeetingScheduleList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.CLIENT_MEETING_SCHEDULE)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader>Date</PdfTableHeader>
        <PdfTableHeader>Meeting With</PdfTableHeader>
        <PdfTableHeader style={{ flex: 3 }}>Notes</PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => (
        <PdfTableRow key={idx}>
          <PdfTableCell>
            {formatDate(row.meeting_date, "DD/MM/YYYY")}
          </PdfTableCell>
          <PdfTableCell>{row.ap_stakeholder_mapping_item?.name}</PdfTableCell>
          <PdfTableCell style={{ flex: 3 }}>{row.notes}</PdfTableCell>
        </PdfTableRow>
      ))}
    </PdfTable>
  );
};
