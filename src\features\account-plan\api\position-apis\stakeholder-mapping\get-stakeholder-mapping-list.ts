import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APStakeholderMapping } from "@/features/account-plan/types/position-types";

type StakeholderMappingListParams = BaseParams;

export const getStakeholderMappingList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: StakeholderMappingListParams;
}): ApiResponse<APStakeholderMapping[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_STAKEHOLDER_MAPPING(accountId), {
    params,
  });
};

export const getStakeholderMappingListQueryOptions = (
  accountId: number,
  params?: StakeholderMappingListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_STAKEHOLDER_MAPPING,
    ],
    queryFn: () => getStakeholderMappingList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseStakeholderMappingListOptions = {
  params?: StakeholderMappingListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getStakeholderMappingList>;
  options?: Partial<ReturnType<typeof getStakeholderMappingListQueryOptions>>;
};

export const useStakeholderMappingList = ({
  accountId,
  params,
  queryConfig,
  options,
}: UseStakeholderMappingListOptions) => {
  const stakeholderMappingListQuery = useQuery({
    ...getStakeholderMappingListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...stakeholderMappingListQuery,
    stakeholderMappingList: stakeholderMappingListQuery.data?.data,
  };
};
