"use client";
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react/display-name */

import Link from "next/link";
import { PATH } from "@/constants/path";
import { IconHandStop } from "@tabler/icons-react";
import { useAuthStore } from "../stores/auth-store";
import { useRolesPermissions } from "@/features/user-management/hooks/use-role-permissions";

import { useAuth } from "../api/get-auth";
import { UserRole } from "../types/user";

export default function withProtectedRoute(
  Component: any,
  minRoleEligibility?: UserRole
) {
  return (props: any) => {
    const { isLogin, _hasHydrated } = useAuthStore();
    const { isPending } = useAuth({});
    const { checkEligibility } = useRolesPermissions();

    if (!_hasHydrated || (isLogin && isPending)) return null;

    if (!isLogin) {
      return (
        <div className="flex h-screen w-full flex-col items-center justify-center gap-4">
          <IconHandStop className="h-24 w-24" />
          <h1 className="text-2xl font-semibold">
            You don't have access to view this page
          </h1>
          <p>
            Please{" "}
            <Link
              className="underline underline-offset-2 hover:font-semibold"
              href={PATH.LANDING}
            >
              sign in
            </Link>{" "}
            to continue.
          </p>
        </div>
      );
    }

    if (!!minRoleEligibility && !checkEligibility(minRoleEligibility)) {
      return (
        <div className="-my-res-y-3xl flex h-screen w-full flex-col items-center justify-center gap-res-y-base">
          <IconHandStop className="h-24 w-24" />
          <h1 className="text-2xl font-semibold">Restricted page</h1>
          <p>
            Only authorized users with the appropriate role can access this
            page.
          </p>
        </div>
      );
    }

    return <Component {...props} />;
  };
}
