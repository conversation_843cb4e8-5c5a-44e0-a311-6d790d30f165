import { useAuth } from "@/features/auth/api/get-auth";
import { rolePriority } from "@/features/auth/constants";
import { UserRole } from "@/features/auth/types/user";
import { useCallback, useMemo } from "react";
import { rolesList } from "../constants";
import { OrganizationTier } from "@/features/organizations/types";

export const useRolesPermissions = () => {
  const { user } = useAuth({});

  const role = user.role ?? UserRole.MEMBER;

  const checkEligibility = (minRole: UserRole) => {
    return rolePriority[role] <= rolePriority[minRole];
  };

  const checkIsLowerRole = useCallback(
    (targetRole: UserRole) => {
      return rolePriority[role] < rolePriority[targetRole];
    },
    [role]
  );

  const checkIsEqualOrLowerRole = useCallback(
    (targetRole: UserRole) => {
      return rolePriority[role] <= rolePriority[targetRole];
    },
    [role]
  );

  const assignableUserRolesList = useMemo(() => {
    return rolesList.filter((v) => checkIsEqualOrLowerRole(v.value));
  }, [checkIsEqualOrLowerRole]);

  return {
    checkEligibility,
    checkIsLowerRole,
    checkIsEqualOrLowerRole,
    assignableUserRolesList,
    isAbleToInvite: checkEligibility(UserRole.SUPER_ADMIN),
    isAbleToModifyUser: checkEligibility(UserRole.SUPER_ADMIN),
    isAbleToModfiyPermissions: checkEligibility(UserRole.OWNER),
    isAbleToManageOrganization: checkEligibility(UserRole.SUPER_ADMIN),
    isAbleToManageTemplate:
      checkEligibility(UserRole.OWNER) &&
      user.organization?.tier === OrganizationTier.SUPERUSER,
    isAbleToManageSettings:
      checkEligibility(UserRole.OWNER) &&
      user.organization?.tier === OrganizationTier.SUPERUSER,
    isAbleToManageOtherOrganizations:
      checkEligibility(UserRole.OWNER) &&
      user.organization?.tier === OrganizationTier.SUPERUSER,
  };
};
