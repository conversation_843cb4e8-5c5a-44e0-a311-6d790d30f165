import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { AccountPlanData, AccountPlanPayload } from "../types";

type AccountListParams = BaseParams & Pick<AccountPlanPayload, "status">;

export const getAccountPlanList = ({
  params,
}: {
  params?: AccountListParams;
}): ApiResponse<AccountPlanData[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS, { params });
};

export const getAccountPlanListQueryOptions = (params?: AccountListParams) => {
  return queryOptions({
    queryKey: [QUERY_KEYS.ACCOUNT_PLANS, params],
    queryFn: () => getAccountPlanList({ params }),
  });
};

type UseAccountPlanOptions = {
  params?: AccountListParams;
  queryConfig?: QueryConfig<typeof getAccountPlanList>;
};

export const useAccountPlanList = ({
  queryConfig,
  params,
}: UseAccountPlanOptions) => {
  const accountPlanListQuery = useQuery({
    ...getAccountPlanListQueryOptions(params),
    ...queryConfig,
  });

  return {
    ...accountPlanListQuery,
    accountPlanList: accountPlanListQuery.data?.data || [],
  };
};
