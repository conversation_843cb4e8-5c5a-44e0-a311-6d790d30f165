import _ from "lodash";

import { formatDecimalValue } from "@/lib/utils/table-utils";

import {
  PdfTable,
  PdfTable<PERSON>ell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "../";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { walletShareOptions } from "../../../tables/position-tables/wallet-share-table";
import { APWalletShareType } from "@/features/account-plan/types/position-types";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const WalletSharePDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["walletShareList"];
}) => {
  const addressableValue = data?.[0].shared_type_analysis ?? 0;
  const existingValue = data?.[1].shared_type_analysis ?? 0;
  const competitionValue = data?.[2].shared_type_analysis ?? 0;
  const availableValue = addressableValue - existingValue - competitionValue;

  return (
    <PdfTable>
      <PdfTableTitle>
        {" "}
        {getAccountPlanTableName(AccountPlanTableType.WALLET_SHARE)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader style={{ flex: 1.25 }}> </PdfTableHeader>
        <PdfTableHeader>%</PdfTableHeader>
        <PdfTableHeader>Value (SGD)</PdfTableHeader>
        <PdfTableHeader style={{ flex: 3.5 }}>
          Services and Product Description
        </PdfTableHeader>
      </PdfTableRow>

      {data?.map((row, idx) => {
        const value =
          row.item_type === APWalletShareType.AVAILABLE
            ? availableValue
            : (row.shared_type_analysis ?? 0);

        const percentage =
          addressableValue === 0 ? 0 : (value / addressableValue) * 100;

        return (
          <PdfTableRow key={idx}>
            <PdfTableCell style={{ flex: 1.25, fontWeight: 700 }}>
              {walletShareOptions[row.item_type].name}
            </PdfTableCell>
            <PdfTableCell>{percentage.toFixed(2)}%</PdfTableCell>
            <PdfTableCell>
              {formatDecimalValue({ value: row.shared_type_analysis })}
            </PdfTableCell>
            <PdfTableCell style={{ flex: 3.5 }}>{row.description}</PdfTableCell>
          </PdfTableRow>
        );
      })}
    </PdfTable>
  );
};
