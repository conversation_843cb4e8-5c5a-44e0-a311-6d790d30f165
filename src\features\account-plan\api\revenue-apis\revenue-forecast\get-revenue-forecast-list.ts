import { queryOptions, useQuery } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APRevenueForecast } from "@/features/account-plan/types/revenue-types";

type RevenueForecastListParams = BaseParams;

export const getRevenueForecastList = ({
  accountId,
}: {
  accountId: number;
  params?: RevenueForecastListParams;
}): ApiResponse<APRevenueForecast[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_REVENUE_FORECAST(accountId));
};

export const getRevenueForecastListQueryOptions = (
  accountId: number,
  params?: RevenueForecastListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_REVENUE_FORECAST,
    ],
    queryFn: () => getRevenueForecastList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseRevenueForecastListOptions = {
  params?: RevenueForecastListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getRevenueForecastList>;
  options?: Partial<ReturnType<typeof getRevenueForecastListQueryOptions>>;
};

export const useRevenueForecastList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseRevenueForecastListOptions) => {
  const revenueForecastListQuery = useQuery({
    ...getRevenueForecastListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...revenueForecastListQuery,
    revenueForecastList: revenueForecastListQuery.data?.data,
  };
};
