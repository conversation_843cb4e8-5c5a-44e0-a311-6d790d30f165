import { useMutation, useQueryClient } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, MutationConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import {
  APStakeholderMapping,
  APStakeholderMappingBaseData,
} from "@/features/account-plan/types/position-types";
import { QUERY_KEYS } from "@/constants/query-keys";

export const createStakeholderMapping = ({
  accountId,
  data,
}: {
  accountId: number;
  data?: APStakeholderMappingBaseData;
}): ApiResponse<APStakeholderMapping> => {
  return api.post(
    API_ROUTES.ACCOUNT_PLANS_STAKEHOLDER_MAPPING(accountId),
    data
  );
};

type UseCreateStakeholderMappingOptions = {
  mutationConfig?: MutationConfig<typeof createStakeholderMapping>;
};

export const useCreateStakeholderMapping = ({
  mutationConfig,
}: UseCreateStakeholderMappingOptions) => {
  const queryClient = useQueryClient();
  const { onSuccess, invalidate, ...restConfig } = mutationConfig || {};

  return useMutation({
    onSuccess: async (...args) => {
      if (invalidate) {
        await queryClient.invalidateQueries({
          queryKey: [
            QUERY_KEYS.ACCOUNT_PLANS,
            args[1].accountId,
            QUERY_KEYS.ACCOUNT_PLANS_STAKEHOLDER_MAPPING,
          ],
        });
      }

      await queryClient.invalidateQueries({
        queryKey: [
          QUERY_KEYS.ACCOUNT_PLANS,
          args[1].accountId,
          QUERY_KEYS.ACCOUNT_PLANS_TARGETED_PERCEPTION_DEVELOPMENT,
        ],
      });

      onSuccess?.(...args);
    },
    ...restConfig,
    mutationFn: createStakeholderMapping,
  });
};
