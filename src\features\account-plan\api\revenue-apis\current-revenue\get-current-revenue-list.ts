import { queryOptions, useQuery, useQueries } from "@tanstack/react-query";

import { api } from "@/lib/api-client";
import { ApiResponse, BaseParams, QueryConfig } from "@/lib/react-query";
import { API_ROUTES } from "@/constants/api-routes";
import { QUERY_KEYS } from "@/constants/query-keys";
import { APCurrentRevenue } from "@/features/account-plan/types/revenue-types";

type CurrentRevenueListParams = BaseParams;

export const getCurrentRevenueList = ({
  accountId,
  params,
}: {
  accountId: number;
  params?: CurrentRevenueListParams;
}): ApiResponse<APCurrentRevenue[]> => {
  return api.get(API_ROUTES.ACCOUNT_PLANS_CURRENT_REVENUE(accountId), {
    params,
  });
};

export const getCurrentRevenueListQueryOptions = (
  accountId: number,
  params?: CurrentRevenueListParams
) => {
  return queryOptions({
    queryKey: [
      QUERY_KEYS.ACCOUNT_PLANS,
      accountId,
      QUERY_KEYS.ACCOUNT_PLANS_CURRENT_REVENUE,
    ],
    queryFn: () => getCurrentRevenueList({ accountId, params }),
    enabled: !!accountId,
  });
};

type UseCurrentRevenueListOptions = {
  params?: CurrentRevenueListParams;
  accountId: number;
  queryConfig?: QueryConfig<typeof getCurrentRevenueList>;
  options?: Partial<ReturnType<typeof getCurrentRevenueListQueryOptions>>;
};

export const useCurrentRevenueList = ({
  params,
  accountId,
  queryConfig,
  options,
}: UseCurrentRevenueListOptions) => {
  const currentRevenueListQuery = useQuery({
    ...getCurrentRevenueListQueryOptions(accountId, params),
    ...queryConfig,
    ...options,
  });

  return {
    ...currentRevenueListQuery,
    currentRevenueList: currentRevenueListQuery.data?.data,
  };
};

type UseCurrentRevenueListsOptions = {
  queries: {
    accountId: number;
    params?: CurrentRevenueListParams;
  }[];
  queryConfig?: QueryConfig<typeof getCurrentRevenueList>;
  options?: Partial<ReturnType<typeof getCurrentRevenueListQueryOptions>>;
};

export const useCurrentRevenueLists = ({
  queries,
  queryConfig,
}: UseCurrentRevenueListsOptions) => {
  const results = useQueries({
    queries: queries.map(({ accountId, params }) => ({
      ...getCurrentRevenueListQueryOptions(accountId, params),
      ...queryConfig,
    })),
  });

  return results.map((result, idx) => ({
    ...result,
    query: queries[idx],
    currentRevenueList: result.data?.data ?? [],
  }));
};
