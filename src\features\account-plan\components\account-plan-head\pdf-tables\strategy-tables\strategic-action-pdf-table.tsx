import _ from "lodash";

import {
  PdfTable,
  PdfTableCell,
  PdfTableHeader,
  PdfTableRow,
  PdfTableTitle,
} from "..";
import { AccountPlanAnalysisPDFProps } from "../../download-analysis";
import { formatDate } from "@/lib/utils";
import { getAccountPlanTableName } from "@/features/account-plan/constants";
import { AccountPlanTableType } from "@/features/account-plan/types";

export const StrategicActionPDFTable = ({
  data,
}: {
  data?: AccountPlanAnalysisPDFProps["topActionList"];
}) => {
  return (
    <PdfTable>
      <PdfTableTitle>
        {getAccountPlanTableName(AccountPlanTableType.TOP_ACTION)}
      </PdfTableTitle>

      <PdfTableRow>
        <PdfTableHeader>What will you do?</PdfTableHeader>
        <PdfTableHeader>How will you do it?</PdfTableHeader>
        <PdfTableHeader style={{ flex: 0.5 }}>Owner</PdfTableHeader>
        <PdfTableHeader style={{ flex: 0.5 }}>Due Date</PdfTableHeader>
      </PdfTableRow>

      {data
        ?.sort((a, b) => a.order - b.order)
        .map((row, idx) => (
          <PdfTableRow key={idx}>
            <PdfTableCell>{row.description}</PdfTableCell>
            <PdfTableCell>{row.how}</PdfTableCell>
            <PdfTableCell style={{ flex: 0.5 }}>
              {row.action_target}
            </PdfTableCell>
            <PdfTableCell style={{ flex: 0.5 }}>
              {formatDate(row.action_date, "DD/MM/YYYY")}
            </PdfTableCell>
          </PdfTableRow>
        ))}
    </PdfTable>
  );
};
